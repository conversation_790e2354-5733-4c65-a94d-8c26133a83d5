/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    tim.c
  * @brief   This file provides code for the configuration
  *          of the TIM instances.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2024 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "tim.h"

/* USER CODE BEGIN 0 */
#include "can.h"
#include "string.h"
extern uint8_t Rxdata[8];
extern unsigned char Can_stop_buff[8];
extern  void CAN_SendMessage(uint32_t id, uint8_t *data, uint8_t len);
/* USER CODE END 0 */

TIM_HandleTypeDef htim14;

/* TIM14 init function */
void MX_TIM14_Init(void)
{

  /* USER CODE BEGIN TIM14_Init 0 */

  /* USER CODE END TIM14_Init 0 */

  /* USER CODE BEGIN TIM14_Init 1 */

  /* USER CODE END TIM14_Init 1 */
  htim14.Instance = TIM14;
  htim14.Init.Prescaler = 7200-1;
  htim14.Init.CounterMode = TIM_COUNTERMODE_UP;
  htim14.Init.Period = 30000-1;
  htim14.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
  htim14.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_DISABLE;
  if (HAL_TIM_Base_Init(&htim14) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN TIM14_Init 2 */

  /* USER CODE END TIM14_Init 2 */

}

void HAL_TIM_Base_MspInit(TIM_HandleTypeDef* tim_baseHandle)
{

  if(tim_baseHandle->Instance==TIM14)
  {
  /* USER CODE BEGIN TIM14_MspInit 0 */

  /* USER CODE END TIM14_MspInit 0 */
    /* TIM14 clock enable */
    __HAL_RCC_TIM14_CLK_ENABLE();

    /* TIM14 interrupt Init */
    HAL_NVIC_SetPriority(TIM8_TRG_COM_TIM14_IRQn, 0, 0);
    HAL_NVIC_EnableIRQ(TIM8_TRG_COM_TIM14_IRQn);
  /* USER CODE BEGIN TIM14_MspInit 1 */

  /* USER CODE END TIM14_MspInit 1 */
  }
}

void HAL_TIM_Base_MspDeInit(TIM_HandleTypeDef* tim_baseHandle)
{

  if(tim_baseHandle->Instance==TIM14)
  {
  /* USER CODE BEGIN TIM14_MspDeInit 0 */

  /* USER CODE END TIM14_MspDeInit 0 */
    /* Peripheral clock disable */
    __HAL_RCC_TIM14_CLK_DISABLE();

    /* TIM14 interrupt Deinit */
    HAL_NVIC_DisableIRQ(TIM8_TRG_COM_TIM14_IRQn);
  /* USER CODE BEGIN TIM14_MspDeInit 1 */

  /* USER CODE END TIM14_MspDeInit 1 */
  }
}

/* USER CODE BEGIN 1 */
//-------------------��ʱ���Ļص�����-------------------------
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{

  if (htim->Instance == TIM14)
  {
		 // ÿ�ζ�ʱ�����ڷ���һ��ת�ٸ��ƽ���
    if (__HAL_TIM_GET_COUNTER(&htim14) >= 1000 && current_can_id != 0) // ���磺1�볬ʱ
    {
      // ���͵�ǰ��ת�ٸ�ָ�����ƽ���
      uint8_t message_data[8];
      memcpy(message_data + 2, &current_speed, sizeof(current_speed)); // �����ٶ�ֵ
      CAN_SendMessage(current_can_id, message_data, 8);

      // ���ö�ʱ��������״̬
      __HAL_TIM_SET_COUNTER(&htim14, 0);
      current_can_id = 0; // �����ǰCAN ID
    }
    // �����ʱ��������û���µ�CAN��Ϣ����ֹͣ�ƽ���
    if (__HAL_TIM_GET_COUNTER(&htim14) >= 30000 && propulsionCommand != 0) // ���磺3�볬ʱ
    {
      CAN_SendMessage((uint32_t)propulsor_ids, Can_stop_buff, 8);
      propulsionCommand = 0; // ֹͣ������Ϊ0
      __HAL_TIM_SET_COUNTER(&htim14, 0); // ���ö�ʱ��
			 current_can_id = 0; // �����ǰCAN ID
    }
  }
}
/* USER CODE END 1 */
