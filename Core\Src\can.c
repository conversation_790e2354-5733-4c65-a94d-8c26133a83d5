/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    tim.c
  * @brief   This file provides code for the configuration
  *          of the TIM instances.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2024 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "tim.h"

/* USER CODE BEGIN 0 */
#include "can.h"
#include "string.h"
extern uint8_t Rxdata[8];
extern unsigned char Can_stop_buff[8];
extern  void CAN_SendMessage(uint32_t id, uint8_t *data, uint8_t len);
/* USER CODE END 0 */

TIM_HandleTypeDef htim14;

/* TIM14 init function */
void MX_TIM14_Init(void)
{

  /* USER CODE BEGIN TIM14_Init 0 */

  /* USER CODE END TIM14_Init 0 */

  /* USER CODE BEGIN TIM14_Init 1 */

  /* USER CODE END TIM14_Init 1 */
  htim14.Instance = TIM14;
  htim14.Init.Prescaler = 7200-1;
  htim14.Init.CounterMode = TIM_COUNTERMODE_UP;
  htim14.Init.Period = 30000-1;
  htim14.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
  htim14.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_DISABLE;
  if (HAL_TIM_Base_Init(&htim14) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN TIM14_Init 2 */

  /* USER CODE END TIM14_Init 2 */

}

void HAL_TIM_Base_MspInit(TIM_HandleTypeDef* tim_baseHandle)
{

  if(tim_baseHandle->Instance==TIM14)
  {
  /* USER CODE BEGIN TIM14_MspInit 0 */

  /* USER CODE END TIM14_MspInit 0 */
    /* TIM14 clock enable */
    __HAL_RCC_TIM14_CLK_ENABLE();

    /* TIM14 interrupt Init */
    HAL_NVIC_SetPriority(TIM8_TRG_COM_TIM14_IRQn, 0, 0);
    HAL_NVIC_EnableIRQ(TIM8_TRG_COM_TIM14_IRQn);
  /* USER CODE BEGIN TIM14_MspInit 1 */

  /* USER CODE END TIM14_MspInit 1 */
  }
}

void HAL_TIM_Base_MspDeInit(TIM_HandleTypeDef* tim_baseHandle)
{

  if(tim_baseHandle->Instance==TIM14)
  {
  /* USER CODE BEGIN TIM14_MspDeInit 0 */

  /* USER CODE END TIM14_MspDeInit 0 */
    /* Peripheral clock disable */
    __HAL_RCC_TIM14_CLK_DISABLE();

    /* TIM14 interrupt Deinit */
    HAL_NVIC_DisableIRQ(TIM8_TRG_COM_TIM14_IRQn);
  /* USER CODE BEGIN TIM14_MspDeInit 1 */

  /* USER CODE END TIM14_MspDeInit 1 */
  }
}

/* USER CODE BEGIN 1 */
//-------------------锟斤拷时锟斤拷锟侥回碉拷锟斤拷锟斤拷-------------------------
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{

  if (htim->Instance == TIM14)
  {
		 // 每锟轿讹拷时锟斤拷锟斤拷锟节凤拷锟斤拷一锟斤拷转锟劫革拷锟狡斤拷锟斤拷
    if (__HAL_TIM_GET_COUNTER(&htim14) >= 1000 && current_can_id != 0) // 锟斤拷锟界：1锟诫超时
    {
      // 锟斤拷锟酵碉拷前锟斤拷转锟劫革拷指锟斤拷锟斤拷锟狡斤拷锟斤拷
      uint8_t message_data[8];
      memcpy(message_data + 2, &current_speed, sizeof(current_speed)); // 锟斤拷锟斤拷锟劫讹拷值
      CAN_SendMessage(current_can_id, message_data, 8);

      // 锟斤拷锟矫讹拷时锟斤拷锟斤拷锟斤拷锟斤拷状态
      __HAL_TIM_SET_COUNTER(&htim14, 0);
      current_can_id = 0; // 锟斤拷锟斤拷锟角癈AN ID
    }
    // 锟斤拷锟斤拷锟绞憋拷锟斤拷锟斤拷锟斤拷锟矫伙拷锟斤拷碌锟紺AN锟斤拷息锟斤拷锟斤拷停止锟狡斤拷锟斤拷
    if (__HAL_TIM_GET_COUNTER(&htim14) >= 30000 && propulsionCommand != 0) // 锟斤拷锟界：3锟诫超时
    {
      CAN_SendMessage((uint32_t)propulsor_ids, Can_stop_buff, 8);
      propulsionCommand = 0; // 停止锟斤拷锟斤拷锟斤拷为0
      __HAL_TIM_SET_COUNTER(&htim14, 0); // 锟斤拷锟矫讹拷时锟斤拷
			 current_can_id = 0; // 锟斤拷锟斤拷锟角癈AN ID
    }
  }
}
/* USER CODE END 1 */
			if (is_first_boot) {
			Write_CAN_IDs_To_EEPROM();
			is_first_boot = false;  // 鏍囪涓洪潪棣栨鍚姩
		}
	//锟斤拷取8锟斤拷CAN_ID
		Read_CAN_IDs_From_EEPROM(0,8);
	/*-------------------------------------------------*/
	
	/*--------------------锟斤拷时锟斤拷14锟斤拷锟斤拷锟斤拷----------------------*/
	// 锟斤拷锟斤拷锟斤拷时锟斤拷14
   HAL_TIM_Base_Start_IT(&htim14);
	 // CAN锟侥斤拷锟斤拷锟叫断匡拷始
   HAL_CAN_ActivateNotification(&hcan1, CAN_IT_RX_FIFO0_MSG_PENDING);
   //SendTestCanMessage();
	/*-------------------------------------------------*/
	
  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
 }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_RCC_PWR_CLK_ENABLE();
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSI;
  RCC_OscInitStruct.PLL.PLLM = 8;
  RCC_OscInitStruct.PLL.PLLN = 72;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
  RCC_OscInitStruct.PLL.PLLQ = 4;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV2;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_2) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
 if(HAL_CAN_ConfigFilter(&hcan1,&sFilterConfig) != HAL_OK)//初始化过滤器
 {
  Error_Handler();
 }
 if(HAL_CAN_Start(&hcan1) != HAL_OK)//打开can
 {
  Error_Handler();
 }
 if(HAL_CAN_ActivateNotification(&hcan1,CAN_IT_RX_FIFO0_MSG_PENDING) != HAL_OK)//开启接受邮邮箱0挂起中断
 {
  Error_Handler();
 }
 
}

//------------------CAN2配置--------------------------------//
void CAN2_Config(void)
{	
 
	CAN_FilterTypeDef  sFilterConfig;
 
  /*配置CAN过滤器*/
  sFilterConfig.FilterBank = 0;                     //过滤器0
  sFilterConfig.FilterMode = CAN_FILTERMODE_IDMASK;
  sFilterConfig.FilterScale = CAN_FILTERSCALE_32BIT;
  sFilterConfig.FilterIdHigh = 0x0000;              //32位ID
  sFilterConfig.FilterIdLow = 0x0000;
  sFilterConfig.FilterMaskIdHigh = 0x0000;          //32位MASK
  sFilterConfig.FilterMaskIdLow = 0x0000;
  sFilterConfig.FilterFIFOAssignment = CAN_RX_FIFO0;//过滤器0关联到FIFO0
  sFilterConfig.FilterActivation = ENABLE;          //激活滤波器0
  sFilterConfig.SlaveStartFilterBank = 14;
	
	
 if(HAL_CAN_ConfigFilter(&hcan2,&sFilterConfig) != HAL_OK)//初始化过滤器
 {
  Error_Handler();
 }
 if(HAL_CAN_Start(&hcan2) != HAL_OK)//打开can
 {
  Error_Handler();
 }
 if(HAL_CAN_ActivateNotification(&hcan2,CAN_IT_RX_FIFO0_MSG_PENDING) != HAL_OK)//开启接受邮邮箱0挂起中断
 {
  Error_Handler();
 }
 
}

//-------------------------------CAN模拟主控发送数据的函数-----------------------// 
// void SendTestCanMessage(void)
// {
//     // 构建一个2字节的测试消息
//     uint8_t testData[2];
//     testData[0] = 0x01; // 0x01是前进命令
//     testData[1] = 0x7F; // 速度值 (127)
	
//     // 设置发送的消息ID（标准ID格式）
//     uint32_t testId = 0x31B; 

//     // 发送CAN消息
//     CAN_SendMessage(testId, testData, 2);

//     // 延时一段时间确保消息被发送出去
//     HAL_Delay(100);
// }
//---------------------------------------------------------------------------//


/* 函数名称：从两个字节中提取推进器命令
	*函数参数：uint8_t high_byte, uint8_t low_byte
	*函数返回值：结构体
	*函数作用：从接收到的两个字节中提取速度和方向信息
*/ 
PropulsorCommand ExtractPropulsorCommand(uint8_t high_byte, uint8_t low_byte)
{
    // 提取推进器命令
    PropulsorCommand cmd;
    // 一共两个字节，提取其中的高字节来进行判断正反，假设200的话就是00 C8，那么就是正转
    cmd.raw_speed = high_byte;
    // 判断方向
    cmd.direction = (cmd.raw_speed & DIRECTION_BIT) ? DIRECTION_REVERSE : DIRECTION_FORWARD;
    // 提取速度值
    cmd.speed = cmd.raw_speed & SPEED_MASK;
    return cmd;
}

/* 函数名称：准备发送缓冲区
	*函数参数：PropulsorCommand cmd, uint8_t *buffer
	*函数返回值：无返回值
	*函数作用：将命令打包成标准格式的8字节消息
*/ 
void PrepareCommandBuffer(PropulsorCommand cmd, uint8_t *buffer)
{
    // 填充命令头
    buffer[0] = CMD_HEADER_1;
    buffer[1] = CMD_HEADER_2;
    buffer[2] = CMD_RESERVED_1;
    buffer[3] = CMD_RESERVED_2;
    // 填充方向位
    if (cmd.direction == DIRECTION_FORWARD) {
        buffer[4] = 0x00;
        buffer[5] = 0x00;
    } else {
        buffer[4] = CMD_REVERSE_BYTE;
        buffer[5] = CMD_REVERSE_BYTE;
    }
    // 填充速度值
    buffer[6] = (cmd.speed >> 8) & 0xFF;
    buffer[7] = cmd.speed & 0xFF;
}

/* 函数名称：发送推进器命令
	*函数参数：uint32_t can_id, PropulsorCommand cmd
	*函数返回值：无返回值
	*函数作用：用来将处理好的数据发送出去
*/ 
void SendPropulsorCommand(uint32_t can_id, PropulsorCommand cmd)
{
    uint8_t buffer[8];
    // 准备命令缓冲区
    PrepareCommandBuffer(cmd, buffer);
    // 发送CAN消息
    CAN_SendMessage(can_id, buffer, 8);
}

/* 函数名称：处理推进器命令
	*函数参数：uint8_t *data, uint8_t start_index, uint8_t count
	*函数返回值：无返回值
	*函数作用：用来处理数据
*/ 
void ProcessPropulsorCommands(uint8_t *data, uint8_t start_index, uint8_t count)
{
    for (uint8_t i = 0; i < count; i++)
    {
        // 提取推进器命令
        PropulsorCommand cmd = ExtractPropulsorCommand(data[i*2], data[i*2 + 1]);
        // 发送推进器命令
        SendPropulsorCommand(CAN_ID[start_index + i], cmd);
        // 短暂延时确保消息发送
        HAL_Delay(10);  
    }
}
//-------------------------------CAN中断程序-----------------------------------//
// 接收：CAN总线 -> 中断处理 -> 消息解析 -> 命令提取 -> 执行操作
// 发送：命令生成 -> 数据打包 -> CAN消息构建 -> 发送到总线
void HAL_CAN_RxFifo0MsgPendingCallback(CAN_HandleTypeDef *hcan)
{
    if (hcan->Instance == CAN1)
  {   
        CAN_RxHeaderTypeDef rxHeader;
        uint8_t data[8];
        
        if (HAL_CAN_GetRxMessage(hcan, CAN_RX_FIFO0, &rxHeader, data) == HAL_OK)
        {
            switch(rxHeader.StdId)
            {
                case MSG_ID_PROPULSOR_CMD_1:					//----对应101
                    ProcessPropulsorCommands(data, 0, 4);
                    break;
                case MSG_ID_PROPULSOR_CMD_2:					//102命令控制
                    ProcessPropulsorCommands(data, 4, 4);
                    break;
                case MSG_ID_CONFIG_1:									//写入操作命令103
                    // 更新前4个CAN ID
                    for(int i = 0; i < 4; i++)
                    {
                        CAN_ID[i] = (uint16_t)(data[i*2] << 8) | data[i*2+1];
                    }
                    Write_CAN_IDs_To_EEPROM();
                    break;
                    
                case MSG_ID_CONFIG_2:									//写入操作命令104
                    // 更新后4个CAN ID
                    for(int i = 0; i < 4; i++)
                    {
                        CAN_ID[i+4] = (uint16_t)(data[i*2] << 8) | data[i*2+1];
                    }
                    Write_CAN_IDs_To_EEPROM();
                    break;
            }
        }
    }
}
//-------------------------------CAN发送程序-----------------------------------//
void CAN_SendMessage(uint32_t id, uint8_t *data, uint8_t len)
{
    CAN_TxHeaderTypeDef txMessage;
    uint32_t mailbox;
    HAL_StatusTypeDef status;

    txMessage.StdId = id;
    txMessage.ExtId = 0;
    txMessage.RTR = CAN_RTR_DATA;
    txMessage.IDE = CAN_ID_STD;
    txMessage.DLC = len;

    // 发送CAN消息并检查结果
    status = HAL_CAN_AddTxMessage(&hcan1, &txMessage, data, &mailbox);
    if (status != HAL_OK)
    {
        // 发送失败处理
        Error_Handler();
    }

    // 等待发送完成
    while(HAL_CAN_GetTxMailboxesFreeLevel(&hcan1) != 3) {}
}
//---------------------------// 发送命令给推进器的辅助函数-----------------------//
// 发送命令给推进器的辅助函数
void SendCommandToPropulsor(int speed)
{
    uint8_t buf[8] = {0};

    buf[0] = 0x54;
    buf[1] = 0x43;
    buf[2] = 0x00; // 第二个字节保留为0x00
    buf[3] = 0x00; // 第三个字节保留为0x00
    buf[4] = speed >> 24;
    buf[5] = speed >> 16;
    buf[6] = speed >> 8;
    buf[7] = speed;

    CAN_SendMessage(SEND_ID, buf, 8);
}

//---------------------------漏水检测代码-----------------------//
//---------------------------漏水检测代码-----------------------//
void Leakage_Detection(void)
{
    static uint8_t Leakage_Detection_data[8] = {0xFB, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};
    const uint16_t DEC_PINS[4] = {GPIO_PIN_0, GPIO_PIN_1, GPIO_PIN_2, GPIO_PIN_4};
    const uint8_t PIN_VALUES[4] = {0x01, 0x02, 0x04, 0x08};  // 对应的状态值
    bool status_changed = false;

    // 检查所有传感器
    for(int i = 0; i < 4; i++) {
        if(HAL_GPIO_ReadPin(GPIOA, DEC_PINS[i]) == RESET) {  // 检测到漏水
            Leakage_Detection_data[i + 1] = PIN_VALUES[i];
            status_changed = true;
        } else {
            Leakage_Detection_data[i + 1] = 0x00;
        }
    }

    // 只在状态发生变化时发送CAN消息
    if(status_changed) {
        // 计算校验和
        uint8_t checksum = Leakage_Detection_data[0];
        for(int i = 1; i <= 4; i++) {
            checksum += Leakage_Detection_data[i];
        }
        Leakage_Detection_data[7] = checksum;
        
        CAN_SendMessage(0x123, Leakage_Detection_data, 8);
    }

    HAL_Delay(100);
}
/* USER CODE END 1 */
