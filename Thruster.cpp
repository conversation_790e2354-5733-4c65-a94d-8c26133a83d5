/************************************************************/
/*    NAME:                                               */
/*    ORGN: MIT                                             */
/*    FILE: Thruster.cpp                                        */
/*    DATE:                                                 */
/************************************************************/

#include <iterator>
#include "MBUtils.h"
#include "Thruster.h"
#include <sstream>
#include <iomanip>
#include <string>
using namespace std;

//---------------------------------------------------------
// Constructor

Thruster::Thruster()
{
  m_iterations = 0;
  m_timewarp   = 1;
  SetAppFreq(10);//设置通信频率和程序频率
  SetCommsFreq(10);

  dT1 = 0;//初始化了一些变量，例如 dT1（主推）、dVT1(垂推)、dST1（侧推）
  dT2 = 0;
  dVT1 = 0;
  dVT2 = 0;
  dVT3 = 0;
  dVT4 = 0;
  dST1 = 0;
  dST2 = 0;
}

//---------------------------------------------------------
// Destructor

Thruster::~Thruster()
{
}

//---------------------------------------------------------
// Procedure: OnNewMail

bool Thruster::OnNewMail(MOOSMSG_LIST &NewMail)
{
    MOOSTrace("This is OnNewMail\n");
    UpdateLocalMOOSVariables(NewMail);
    //MOOSTrace("NewMail Size = %d\n", NewMail.size());
    CMOOSVariable* p = NULL;
	//主推 主推左，主推右
    p = GetMOOSVar("MT1");
    if (p->IsFresh()) {
        dT1 = (*p).GetDoubleVal();
        p->SetFresh(false);
    }
    MOOSTrace("dT1 %d\n",dT1);
    Notify("dT1",dT1);
    p = GetMOOSVar("MT2");
    if (p->IsFresh()) {
        dT2 = (*p).GetDoubleVal();
        p->SetFresh(false);
    }
	Notify("dT2",dT2);
	//垂推 左前推推，右前垂推，左后垂推，右后垂推
     p = GetMOOSVar("MVT1");
    if (p->IsFresh()) {
        dVT1 = (*p).GetDoubleVal();
        p->SetFresh(false);
    }
    Notify("dVT1",dVT1);
     p = GetMOOSVar("MVT2");
    if (p->IsFresh()) {
        dVT2 = (*p).GetDoubleVal();
        p->SetFresh(false);
    }
    Notify("dVT2",dVT2);
     p = GetMOOSVar("MVT3");
    if (p->IsFresh()) {
        dVT3 = (*p).GetDoubleVal();
        p->SetFresh(false);
    }
    Notify("dVT3",dVT3);
     p = GetMOOSVar("MVT4");
    if (p->IsFresh()) {
        dVT4 = (*p).GetDoubleVal();
        p->SetFresh(false);
    }
    Notify("dVT4",dVT4);
	//测推 前侧推，后侧推
	p = GetMOOSVar("MST1");
    if (p->IsFresh()) {
        dST1 = (*p).GetDoubleVal();
        p->SetFresh(false);
    }
    Notify("dST1",dST1);
    p = GetMOOSVar("MST2");
    if (p->IsFresh()) {
        dST2 = (*p).GetDoubleVal();
        p->SetFresh(false);
    }
	Notify("dST2",dST2);

    return (true);
}
void Thruster::RecvFrame() ////持续接收来自 CAN 的数据帧（固定长度）
{
    while (1)
    {
        vector<uint8_t> Frame;
        int n = RecvSock.RecvBinary(Frame, FRAME_LEN);
        if (n <= 0)
        {
            MOOSTrace("invalid callback: RecvFrame() \n");
            exit(EXIT_FAILURE);
        }
        ParseFrame(Frame);

        //MOOSTrace("FRAME %d  %d %d %d  %d %d %d  %d %d %d  %d %d %d  \n",Frame[0],Frame[1],Frame[2],Frame[3],Frame[4],Frame[5],Frame[6],Frame[7],Frame[8],Frame[9],Frame[10],Frame[11],Frame[12]);


    }
}
void Thruster::ParseFrame(vector<uint8_t> Frame)//通过帧头判断数据类型（例如主推、侧推或漏水检测） 从数据帧中解析设备状态，例如转速、电压、电流，并通过 MOOS 变量通知其他模块
{
    if (Frame.size() != FRAME_LEN)
    {
        MOOSTrace("invalid frame length \n");
        return;
    }

    vector<uint8_t> FrameHeader(Frame.begin(), Frame.begin() + 5);//从接收到的帧中提取前 5 个字节(0到4)，作为帧头（FrameHeader）

    //定义帧头标识符
    vector<uint8_t> MT1Header = { 0x08, 0x00, 0x00, 0x03, 0x01 };//主推左 eg:54 43 00 00 FF FF FF FB
    vector<uint8_t> MT2Header = { 0x08, 0x00, 0x00, 0x03, 0x02 };//主推右
    vector<uint8_t> VT1Header = { 0x08, 0x00, 0x00, 0x03, 0x03 };//左前垂推
    vector<uint8_t> VT2Header = { 0x08, 0x00, 0x00, 0x03, 0x04 };//右前垂推
	vector<uint8_t> VT3Header = { 0x08, 0x00, 0x00, 0x03, 0x05 };//左后垂推
	vector<uint8_t> VT4Header = { 0x08, 0x00, 0x00, 0x03, 0x06 };//右后垂推
	vector<uint8_t> ST1Header = { 0x08, 0x00, 0x00, 0x03, 0x07 };//前测推
	vector<uint8_t> ST2Header = { 0x08, 0x00, 0x00, 0x03, 0x08 };//后测推
	
	if (FrameHeader == MT1Header)
    {
    uint8_t uRpm[4];
    memcpy(uRpm, &Frame[4], 4);// 取出第5~8字节（下标4~7）
    uint32_t rpm_MT1 = *(uint32_t *)uRpm;
    MOOSTrace("主推左转速: %d RPM\n", rpm_MT1);
    Notify("current_rpm_MT1", rpm_MT1);
    }
	if (FrameHeader == MT2Header)
    {
    uint8_t uRpm[4];
    memcpy(uRpm, &Frame[4], 4);// 取出第5~8字节（下标4~7）
    uint32_t rpm_MT2 = *(uint32_t *)uRpm;
    MOOSTrace("主推右转速: %d RPM\n", rpm_MT2);
    Notify("current_rpm_MT2", rpm_MT2);
    }
    if (FrameHeader == VT1Header)
    {
    uint8_t uRpm[4];
    memcpy(uRpm, &Frame[4], 4);// 取出第5~8字节（下标4~7）
    uint32_t rpm_VT1 = *(uint32_t *)uRpm;
    MOOSTrace("左前垂推转速: %d RPM\n", rpm_VT1);
    Notify("current_rpm_VT1", rpm_VT1);
    }
    if (FrameHeader == VT2Header)
    {
    uint8_t uRpm[4];
    memcpy(uRpm, &Frame[4], 4);// 取出第5~8字节（下标4~7）
    uint32_t rpm_VT2 = *(uint32_t *)uRpm;
    MOOSTrace("右前垂推转速: %d RPM\n", rpm_VT2);
    Notify("current_rpm_VT2", rpm_VT2);
    }
    if (FrameHeader == VT3Header)
    {
    uint8_t uRpm[4];
    memcpy(uRpm, &Frame[4], 4);// 取出第5~8字节（下标4~7）
    uint32_t rpm_VT3 = *(uint32_t *)uRpm;
    MOOSTrace("左后垂推转速: %d RPM\n", rpm_VT3);
    Notify("current_rpm_VT3", rpm_VT3);
    }
    if (FrameHeader == VT4Header)
    {
    uint8_t uRpm[4];
    memcpy(uRpm, &Frame[4], 4);// 取出第5~8字节（下标4~7）
    uint32_t rpm_VT4 = *(uint32_t *)uRpm;
    MOOSTrace("右后垂推转速: %d RPM\n", rpm_VT4);
    Notify("current_rpm_VT4", rpm_VT4);
    }
    if (FrameHeader == ST1Header)
    {
    uint8_t uRpm[4];
    memcpy(uRpm, &Frame[4], 4);// 取出第5~8字节（下标4~7）
    uint32_t rpm_ST1 = *(uint32_t *)uRpm;
    MOOSTrace("前测推转速: %d RPM\n", rpm_ST1);
    Notify("current_rpm_ST1", rpm_ST1);
    }
    if (FrameHeader == ST2Header)
    {
    uint8_t uRpm[4];
    memcpy(uRpm, &Frame[4], 4);// 取出第5~8字节（下标4~7）
    uint32_t rpm_ST2 = *(uint32_t *)uRpm;
    MOOSTrace("后侧推转速: %d RPM\n", rpm_ST2);
    Notify("current_rpm_ST2", rpm_ST2);
    }
}

   


//---------------------------------------------------------
// Procedure: OnConnectToServer

bool Thruster::OnConnectToServer()
{
   // register for variables here
   // possibly look at the mission file?
   // m_MissionReader.GetConfigurationParam("Name", <string>);
   // m_Comms.Register("VARNAME", 0);
	
   RegisterVariables();
   return(true);
}

//---------------------------------------------------------
// Procedure: Iterate()
//            happens AppTick times per second

bool Thruster::Iterate()
{
	//修改前4个推进器转速 主推左，主推右，测推前，测推后
    vector<uint8_t> FrameT1 = { 0x08, 0x00, 0x00, 0x01, 0x01,
                                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 };
    //修改后四个推进器转速 垂推左前，垂推右前，垂推左后，垂推右后                               	
    vector<uint8_t> FrameT2 = { 0x08, 0x00, 0x00, 0x01, 0x02,
                                0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 };
    
    //修改前4个推进器canid
    vector<uint8_t> FrameChange1 = { 0x08, 0x00, 0x00, 0x01, 0x03,
                                0x03, 0x01, 0x03, 0x02, 0x03, 0x03, 0x03, 0x04 };
    //修改后4个推进器canid                            
    vector<uint8_t> FrameChange2 = { 0x08, 0x00, 0x00, 0x01, 0x04,
                                0x03, 0x05, 0x03, 0x06, 0x03, 0x07, 0x03, 0x08 };
    
    
    //对第一条命令进行更新
 	int idT1 = (int)dT1;//保证是整数
 	if (idT1 < 0)
 	{

     	FrameT1[5] = 0XFF - (unsigned char)(-idT1 / 256);
     	FrameT1[6] = 0XFF - (unsigned char)(-idT1 % 256) + 0X01;
 	}
 	else if (idT1 >= 0)
 	{
     	FrameT1[5] = (unsigned char)(idT1 / 256);
     	FrameT1[6] = (unsigned char)(idT1 % 256);
 	}
 	int idT2 = (int)dT2;//保证是整数
 	if (idT2 < 0)
 	{

     	FrameT1[7] = 0XFF - (unsigned char)(-idT2 / 256);
     	FrameT1[8] = 0XFF - (unsigned char)(-idT2 % 256) + 0X01;
 	}
 	else if (idT2 >= 0)
 	{
     	FrameT1[7] = (unsigned char)(idT2 / 256);
     	FrameT1[8] = (unsigned char)(idT2 % 256);
 	}
 	int idST1 = (int)dST1;//保证是整数
 	if (idST1 < 0)
 	{

     	FrameT1[9] = 0XFF - (unsigned char)(-idST1 / 256);
     	FrameT1[10] = 0XFF - (unsigned char)(-idST1 % 256) + 0X01;
 	}
 	else if (idST1 >= 0)
 	{
     	FrameT1[9] = (unsigned char)(idST1 / 256);
     	FrameT1[10] = (unsigned char)(idST1 % 256);
 	}
	int idST2 = (int)dST2;//保证是整数
 	if (idST2 < 0)
 	{

     	FrameT1[11] = 0XFF - (unsigned char)(-idST2 / 256);
     	FrameT1[12] = 0XFF - (unsigned char)(-idST2 % 256) + 0X01;
 	}
 	else if (idST2 >= 0)
 	{
     	FrameT1[11] = (unsigned char)(idST2 / 256);
     	FrameT1[12] = (unsigned char)(idST2 % 256);
 	}
	
	//对第二条命令进行更新
	
	int idVT1 = (int)dVT1;//保证是整数
 	if (idVT1 < 0)
 	{

     	FrameT2[5] = 0XFF - (unsigned char)(-idVT1 / 256);
     	FrameT2[6] = 0XFF - (unsigned char)(-idVT1 % 256) + 0X01;
 	}
 	else if (idVT1 >= 0)
 	{
     	FrameT2[5] = (unsigned char)(idVT1 / 256);
     	FrameT2[6] = (unsigned char)(idVT1 % 256);
 	}
 	int idVT2 = (int)dVT2;//保证是整数
 	if (idVT2 < 0)
 	{

     	FrameT2[7] = 0XFF - (unsigned char)(-idVT2 / 256);
     	FrameT2[8] = 0XFF - (unsigned char)(-idVT2 % 256) + 0X01;
 	}
 	else if (idVT2 >= 0)
 	{
     	FrameT2[7] = (unsigned char)(idVT2 / 256);
     	FrameT2[8] = (unsigned char)(idVT2 % 256);
 	}
 	int idVT3 = (int)dVT3;//保证是整数
 	if (idVT3 < 0)
 	{

     	FrameT2[9] = 0XFF - (unsigned char)(-idVT3 / 256);
     	FrameT2[10] =0XFF - (unsigned char)(-idVT3 % 256) + 0X01;
 	}
 	else if (idVT3 >= 0)
 	{
     	FrameT2[9] = (unsigned char)(idVT3 / 256);
     	FrameT2[10] = (unsigned char)(idVT3 % 256);
 	}
 	int idVT4 = (int)dVT4;//保证是整数
 	if (idVT4 < 0)
 	{

     	FrameT2[11] = 0XFF - (unsigned char)(-idVT4 / 256);
     	FrameT2[12] = 0XFF - (unsigned char)(-idVT4 % 256) + 0X01;
 	}
 	else if (idVT4 >= 0)
 	{
     	FrameT2[11] = (unsigned char)(idVT4 / 256);
     	FrameT2[12] = (unsigned char)(idVT4 % 256);
 	}
 	
 	
    SendFrame(FrameT1);
    MOOSPause(500);
    SendFrame(FrameT2);
    MOOSPause(500);//50ms

    //SendFrame(FrameChange1);
    //MOOSPause(50);
    //SendFrame(FrameChange2);
    //MOOSPause(50);

    MOOSTrace(" send frame  \n");
	MOOSTrace("FRAMET1 %d  %d %d %d  %d %d %d  %d %d %d  %d %d %d  \n",FrameT1[0],FrameT1[1],FrameT1[2],FrameT1[3],FrameT1[4],FrameT1[5],FrameT1[6],FrameT1[7],FrameT1[8],FrameT1[9],FrameT1[10],FrameT1[11],FrameT1[12]);
	MOOSTrace("FRAMET2 %d  %d %d %d  %d %d %d  %d %d %d  %d %d %d  \n",FrameT2[0],FrameT2[1],FrameT2[2],FrameT2[3],FrameT2[4],FrameT2[5],FrameT2[6],FrameT2[7],FrameT2[8],FrameT2[9],FrameT2[10],FrameT2[11],FrameT2[12]);
 
  return(true);
}
void Thruster::SendFrame(vector<uint8_t> Frame)
{
    int a = SendSock.SendBinary(Frame);
    //MOOSTrace ("a = %d \n",a);
    m_iterations++;
    //MOOSTrace ("iterate = %d \n",iterate);
}
//---------------------------------------------------------
// Procedure: OnStartUp()
//            happens before connection is open

bool Thruster::OnStartUp()
{	list<string> sParams;
 	CMOOSApp::OnStartUp();
    string sRecvIP;//从配置文件中读取 接收IP    网转串的网接主控     3.5主控 ************   副控************
    if (!m_MissionReader.GetConfigurationParam("RecvIP", sRecvIP))
    {
        MOOSTrace("cannot get RecvIP \n");
        sRecvIP = "0.0.0.0";
    }

    int iRecvPort;//从配置文件中读取 接收端口
    if (!m_MissionReader.GetConfigurationParam("RecvPort", iRecvPort))
    {
        MOOSTrace("cannot get RecvPort \n");
        return false;
    }

    string sDestIP;//从配置文件中读取 目标IP 0.96     发送端口是网转串的can盒，can盒接收各种传感器的can信号，再将can信号转网络信号发给主控
    if (!m_MissionReader.GetConfigurationParam("DestIP", sDestIP))//从配置文件中查找指定的键，并将对应的值存储到提供的变量中。
    {
        MOOSTrace("cannot get DestIP \n");
        return false;
    }

    int iDestPort;//从配置文件中读取 目标端口
    if (!m_MissionReader.GetConfigurationParam("DestPort", iDestPort))
    {
        MOOSTrace("cannot get DestPort \n");
        return false;
    }

    if (!(RecvSock.OpenSocket(sRecvIP, iRecvPort) && RecvSock.BindSocket()))//打开接收的套接字
    {
        return false;
    }

    if (!SendSock.OpenSocket(sDestIP, iDestPort))//打开发送的套接字
    {
        return false;
    }

    pthread_t RecvTid;//创建线程 RecvFrame 用于持续接收 CAN 数据帧

    if (pthread_create(&RecvTid, NULL, &Thruster::RecvFrame, this) != 0)//如果 pthread_create 返回值为0，表示线程创建成功
    {
        MOOSTrace("cannot create recv thread \n");
        return false;
    }
    double df_CAN=0.5;
    AddMOOSVariable("MT1", "DESIRED_MT1", "", df_CAN);//订阅推进器转速
    AddMOOSVariable("MT2", "DESIRED_MT2", "", df_CAN);
    AddMOOSVariable("MVT1", "DESIRED_MVT1", "", df_CAN);
    AddMOOSVariable("MVT2", "DESIRED_MVT2", "", df_CAN);
    AddMOOSVariable("MVT3", "DESIRED_MVT3", "", df_CAN);
    AddMOOSVariable("MVT4", "DESIRED_MVT4", "", df_CAN);
    AddMOOSVariable("MST1", "DESIRED_MST1", "", df_CAN);
    AddMOOSVariable("MST2", "DESIRED_MST2", "", df_CAN);
    
    m_timewarp = GetMOOSTimeWarp();

    RegisterVariables();	
    
    return(true);
}

//---------------------------------------------------------
// Procedure: RegisterVariables

void Thruster::RegisterVariables()
{
   RegisterMOOSVariables();
}
bool Thruster::UpdateLocalMOOSVariables(MOOSMSG_LIST & NewMail) {
	MOOSVARMAP::iterator p;
	for (p = m_MOOSVars.begin(); p != m_MOOSVars.end(); p++) {
		CMOOSVariable &rVar = p->second;
		CMOOSMsg Msg;
		if (m_Comms.PeekMail(NewMail, rVar.GetSubscribeName(), Msg, false, true)) {
			rVar.Set(Msg);
			rVar.SetFresh(true);
		}
	}
	return (true);
}


