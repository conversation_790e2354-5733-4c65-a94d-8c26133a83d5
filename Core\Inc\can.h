/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    can.h
  * @brief   This file contains all the function prototypes for
  *          the can.c file
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2024 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __CAN_H__
#define __CAN_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"

/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

extern CAN_HandleTypeDef hcan1;

extern CAN_HandleTypeDef hcan2;

/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */

void MX_CAN1_Init(void);
void MX_CAN2_Init(void);

/* USER CODE BEGIN Prototypes */
//------------------定义声明和外部引用--------------------------------//
extern uint8_t Rxdata[8];
#define  NUM_PROPULSORS 8
#define RECV_ID 0x282               // 接收测试的CAN ID
#define SEND_ID 0x302               // 发送测试的CAN ID
extern unsigned char Can_stop_buff[8];
extern unsigned char propulsor_forward_buff[8]; // 正向
extern unsigned char propulsor_reverse_buff[8]; // 反向
extern unsigned char Can_stop_buff[8]; // ֹͣ
extern uint32_t current_can_id;
extern int current_speed;
//-----------------------------------------------------------//

//-----------------------CAN ID相关定义------------------------------//
#define CAN_ID_COUNT            8    // CAN ID的数量
#define CAN_ID_BYTES_PER_ID    2    // 每个CAN ID占用2字节
#define CAN_ID_TOTAL_BYTES     (CAN_ID_COUNT * CAN_ID_BYTES_PER_ID)
#define IS_VALID_CAN_ID_INDEX(INDEX) ((INDEX) < CAN_ID_COUNT)
//-----------------------------------------------------------//

//-----------------------推进器相关定义-----------------------------//
#define PROPULSOR_DATA_SIZE     2    // 每个推进器占用2字节数据
#define MAX_PROPULSORS         4    // 每条CAN消息最多处理4个推进器
#define SPEED_MASK         0x7FFF    // 速度值掩码 (15位)
#define DIRECTION_BIT     0x8000    // 方向位掩码
//-----------------------------------------------------------//

//------------------------区别方向定义-----------------------------//
#define DIRECTION_FORWARD    0
#define DIRECTION_REVERSE    1
//-----------------------------------------------------------//

//-----------------------命令格式定义-------------------------//
#define CMD_HEADER_1        0x54
#define CMD_HEADER_2        0x43
#define CMD_RESERVED_1      0x00
#define CMD_RESERVED_2      0x00
#define CMD_REVERSE_BYTE    0xFF
//-----------------------------------------------------------//

//-----------------------消息ID定义---------------------------//
#define MSG_ID_PROPULSOR_CMD_1    0x0101    // 前4个推进器的命令
#define MSG_ID_PROPULSOR_CMD_2    0x0102    // 后4个推进器的命令
#define MSG_ID_CONFIG_1           0x0103    // 配置消息1
#define MSG_ID_CONFIG_2           0x0104    // 配置消息2
//-----------------------------------------------------------//

//---------------------推进器命令结构体----------------------//
typedef struct {
    uint16_t raw_speed;    // 原始速度值（包含方向位）
    uint16_t speed;        // 实际速度值（不含方向位）
    uint8_t direction;     // 方向：0=正向，1=反向
} PropulsorCommand;
//-----------------------------------------------------------//

//-----------------------外部变量声明------------------------//
extern CAN_HandleTypeDef hcan1;
extern CAN_HandleTypeDef hcan2;
extern uint16_t CAN_ID[CAN_ID_COUNT];
//-----------------------------------------------------------//

//------------------------函数声明---------------------------//
void CAN1_Config(void);
void CAN2_Config(void);
void CAN_SendMessage(uint32_t id, uint8_t *data, uint8_t len);
PropulsorCommand ExtractPropulsorCommand(uint8_t high_byte, uint8_t low_byte);
void PrepareCommandBuffer(PropulsorCommand cmd, uint8_t *buffer);
void SendPropulsorCommand(uint32_t can_id, PropulsorCommand cmd);
void ProcessPropulsorCommands(uint8_t *data, uint8_t start_index, uint8_t count);
void Leakage_Detection(void);
//-----------------------------------------------------------//

/* USER CODE END Prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __CAN_H__ */

